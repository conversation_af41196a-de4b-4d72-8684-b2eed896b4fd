import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:providers/login_provider.dart';

import '../widgets/header_widget.dart';

class ProfileScreen extends ConsumerWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with image and welcome message
            const HeaderWidget(
              imagePath: 'assets/images/onboarding_1.png',
              title: 'Profile',
              subtitle: 'Customize your settings',
            ),
            const SizedBox(height: 32),

            // Course Selection Section
            Center(
              child: ConstrainedBox(
                constraints: const BoxConstraints(maxWidth: 450),
                child: FilledButton(
                  onPressed: () {
                    ref.read(loginProvider.notifier).signOut();
                  },
                  child: Text('Sign Out'),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
