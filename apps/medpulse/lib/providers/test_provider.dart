import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:entities/published_test_entity.dart';
import 'package:entities/question_entity.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:providers/cached_doc_provider.dart';
import 'package:providers/common.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../common/string_util.dart';

part 'test_provider.g.dart';

/// Provider for fetching published tests
@riverpod
Future<PublishedTestEntity> test(Ref ref, String testId, {int? version, bool free = true}) async {

  // Determine which document to fetch
  final docId = version != null ? '$testId.$version' : testId;

  final collection = free ? 'published_free' : 'published_tests';

  final json = await ref.watch(cachedDocProvider('$collection/$docId').future);
  if (json == null) {
    final msg = 'testProvider: could not load ${free ? "free" : "premium"} test $testId';
    dbgPrint(msg);
    throw (Exception(msg));
  }

  // Parse the document into a PublishedTestEntity
  return PublishedTestEntity.fromJson(json);
}

