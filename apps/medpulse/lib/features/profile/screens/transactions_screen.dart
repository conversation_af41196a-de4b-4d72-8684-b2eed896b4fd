import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:intl/intl.dart';
import 'package:medpulse/providers/user_provider.dart';
import 'package:medpulse/widgets/constrained_width_app_bar.dart';
import 'package:medpulse/widgets/loading_indicator.dart';
import 'package:entities/transaction_entity.dart';

class TransactionsScreen extends ConsumerWidget {
  const TransactionsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userAsync = ref.watch(userProvider);

    return Scaffold(
      appBar: ConstrainedWidthAppBar(appBar: AppBar(title: const Text('My Transactions'))),
      body: userAsync.when(
        data: (user) {
          if (user == null) {
            return const Center(child: Text('Please sign in to view transactions'));
          }

          return StreamBuilder<QuerySnapshot>(
            stream:
                FirebaseFirestore.instance
                    .collection('transactions')
                    .where('userId', isEqualTo: user.uid)
                    .orderBy('timestamp', descending: true)
                    .snapshots(),
            builder: (context, snapshot) {
              if (snapshot.hasError) {
                return Center(child: Text('Error: ${snapshot.error}'));
              }

              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Center(child: LoadingIndicator());
              }

              final transactions = snapshot.data?.docs ?? [];
              if (transactions.isEmpty) {
                return const Center(child: Text('No transactions found'));
              }

              return ListView.builder(
                itemCount: transactions.length,
                itemBuilder: (context, index) {
                  final transaction = TransactionEntity.fromJson(transactions[index].data() as Map<String, dynamic>);

                  final formattedDate = DateFormat('MMM dd, yyyy HH:mm').format(transaction.timestamp);

                  Color statusColor;
                  String statusText;
                  if (transaction.isRefunded) {
                    statusColor = Colors.orange;
                    statusText = 'Refunded';
                  } else if (transaction.isVoided) {
                    statusColor = Colors.grey;
                    statusText = 'Voided';
                  } else if (transaction.status == 'success') {
                    statusColor = Colors.green;
                    statusText = 'Success';
                  } else {
                    statusColor = Colors.red;
                    statusText = 'Failed';
                  }

                  return Card(
                    margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text('Course: ${transaction.courseId}', style: Theme.of(context).textTheme.titleMedium),
                              Container(
                                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                decoration: BoxDecoration(
                                  color: statusColor.withAlpha(26), // 0.1 * 255 = 25.5, rounded to 26
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Text(
                                  statusText,
                                  style: TextStyle(color: statusColor, fontWeight: FontWeight.bold),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Text('Year: ${transaction.yearId}'),
                          Text('Duration: ${transaction.months} months'),
                          Text('Amount: PKR ${transaction.amount.toStringAsFixed(2)}'),
                          Text('Date: $formattedDate'),
                          if (transaction.message != null)
                            Text(
                              'Note: ${transaction.message}',
                              style: TextStyle(color: Theme.of(context).colorScheme.secondary),
                            ),
                        ],
                      ),
                    ),
                  );
                },
              );
            },
          );
        },
        loading: () => const Center(child: LoadingIndicator()),
        error: (error, stack) => Center(child: Text('Error: $error')),
      ),
    );
  }
}
