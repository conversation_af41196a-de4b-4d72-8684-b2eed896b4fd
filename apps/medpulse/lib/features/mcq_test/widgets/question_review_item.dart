import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:entities/question_entity.dart';
import '../../../providers/question_provider.dart';
import '../../../widgets/cached_image.dart';
import '../../../theme/app_theme.dart';

/// Widget that handles loading a specific question for review
class QuestionReviewItem extends ConsumerWidget {
  final String testId;
  final bool free;
  final int index;
  final Map<int, int> userAnswers;

  const QuestionReviewItem({
    super.key,
    required this.testId,
    required this.free,
    required this.index,
    required this.userAnswers,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Watch the question provider inside this widget to isolate rebuilds
    final questionAsync = ref.watch(questionProvider(testId, index, free: free));

    if (questionAsync.isLoading) {
      return const Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
        child: Center(child: CircularProgressIndicator()),
      );
    }

    if (questionAsync.hasError) {
      return const Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
        child: Center(child: Text('Error loading question')),
      );
    }

    final userAnswerIndex = userAnswers[index];
    final question = questionAsync.value!;

    // Handle different question types
    bool isCorrect = false;
    if (question.type == QuestionType.mcq) {
      // For MCQ questions, compare with correctOptionIndex
      final correctAnswerIndex = question.correctOptionIndex;
      isCorrect = userAnswerIndex == correctAnswerIndex;
    } else if (question.type == QuestionType.flipCard) {
      // For FlipCard questions, 1 means "I got it right"
      isCorrect = userAnswerIndex == 1;
    }

    final isAnswered = userAnswerIndex != null;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
            // Question number and status
            Row(
              children: [
                Text('Question ${index + 1}', style: Theme.of(context).textTheme.titleMedium),
                const Spacer(),
                _StatusBadge(context: context, isAnswered: isAnswered, isCorrect: isCorrect),
              ],
            ),
            const SizedBox(height: 8),

            // Question text
            Text(question.question),
            const SizedBox(height: 16),

            // Options or Answer based on question type
            if (question.type == QuestionType.mcq) ...[
              // MCQ Options
              ...List.generate(
                question.options.length,
                (optionIndex) => _OptionItem(
                  context: context,
                  optionIndex: optionIndex,
                  userAnswerIndex: userAnswerIndex,
                  correctAnswerIndex: question.correctOptionIndex,
                  optionText: question.options[optionIndex],
                ),
              ),
            ] else if (question.type == QuestionType.flipCard) ...[
              // FlipCard Answer
              _FlipCardAnswerItem(
                context: context,
                userAnswerIndex: userAnswerIndex,
                answer: question.answer ?? 'No answer provided',
                answerImage: question.answerImage,
                answerImageWidth: question.answerImageWidth,
                answerImageHeight: question.answerImageHeight,
              ),
            ],

            // Explanation if available
            if (question.explanation != null && question.explanation!.isNotEmpty)
              _Explanation(context: context, explanation: question.explanation!),
        ],
      ),
    );
  }
}

class _FlipCardAnswerItem extends StatelessWidget {
  const _FlipCardAnswerItem({
    super.key,
    required this.context,
    required this.userAnswerIndex,
    required this.answer,
    this.answerImage,
    this.answerImageWidth,
    this.answerImageHeight,
  });

  final BuildContext context;
  final int? userAnswerIndex;
  final String answer;
  final String? answerImage;
  final double? answerImageWidth;
  final double? answerImageHeight;

  @override
  Widget build(BuildContext context) {
    final isCorrect = userAnswerIndex == 1; // 1 means "I got it right"
    final isWrong = userAnswerIndex == 2; // 2 means "I got it wrong"

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color:
            isCorrect
                ? getAppColor(context).correctAnswerBackgroundColor
                : isWrong
                ? getAppColor(context).incorrectAnswerBackgroundColor
                : Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color:
              isCorrect
                  ? getAppColor(context).correctAnswerColor
                  : isWrong
                  ? getAppColor(context).incorrectAnswerColor
                  : Theme.of(context).colorScheme.outline.withAlpha(128), // 0.5 * 255 = 127.5, rounded to 128
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Answer header
          Row(
            children: [
              Text(
                'Answer',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(color: Theme.of(context).colorScheme.primary),
              ),
              const Spacer(),
              if (isCorrect)
                Icon(Icons.check_circle, color: getAppColor(context).correctAnswerColor)
              else if (isWrong)
                Icon(Icons.cancel, color: getAppColor(context).incorrectAnswerColor)
              else
                const Icon(Icons.help_outline, color: Colors.grey),
            ],
          ),
          const SizedBox(height: 8),

          // Answer text
          Text(answer),

          // Answer image if available
          if (answerImage != null && answerImage!.isNotEmpty)
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 16.0),
              child: Center(
                child: CachedImage(
                  key: ValueKey('answer-review-${answerImage!}'),
                  imageWidth: answerImageWidth,
                  imageHeight: answerImageHeight,
                  storagePath: answerImage!,
                  errorWidget: const Icon(Icons.image_not_supported, size: 64),
                ),
              ),
            ),
        ],
      ),
    );
  }
}

class _Explanation extends StatelessWidget {
  const _Explanation({super.key, required this.context, required this.explanation});

  final BuildContext context;
  final String explanation;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(top: 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Explanation:', style: Theme.of(context).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold)),
          const SizedBox(height: 4),
          Text(explanation),
        ],
      ),
    );
  }
}

class _OptionItem extends StatelessWidget {
  const _OptionItem({
    super.key,
    required this.context,
    required this.optionIndex,
    required this.userAnswerIndex,
    required this.correctAnswerIndex,
    required this.optionText,
  });

  final BuildContext context;
  final int optionIndex;
  final int? userAnswerIndex;
  final int correctAnswerIndex;
  final String optionText;

  @override
  Widget build(BuildContext context) {
    final isUserAnswer = userAnswerIndex == optionIndex;
    final isCorrectAnswer = correctAnswerIndex == optionIndex;

    Color backgroundColor;
    Color borderColor;

    if (isUserAnswer && isCorrectAnswer) {
      // User selected correct answer
      backgroundColor = getAppColor(context).correctAnswerBackgroundColor;
      borderColor = getAppColor(context).correctAnswerColor;
    } else if (isUserAnswer && !isCorrectAnswer) {
      // User selected wrong answer
      backgroundColor = getAppColor(context).incorrectAnswerBackgroundColor;
      borderColor = getAppColor(context).incorrectAnswerColor;
    } else if (isCorrectAnswer) {
      // Correct answer (not selected by user)
      backgroundColor = getAppColor(context).correctAnswerBackgroundColor;
      borderColor = getAppColor(context).correctAnswerColor;
    } else {
      // Other options
      backgroundColor = Colors.transparent;
      borderColor = getAppColor(context).notAnsweredColor.withAlpha(128);
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: borderColor),
      ),
      child: Row(
        children: [
          // Option letter
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: isCorrectAnswer
                  ? getAppColor(context).correctAnswerColor
                  : (isUserAnswer
                      ? getAppColor(context).incorrectAnswerColor
                      : getAppColor(context).notAnsweredColor.withAlpha(128)),
            ),
            child: Center(
              child: Text(
                String.fromCharCode(65 + optionIndex),
                style: TextStyle(
                  color: isCorrectAnswer || isUserAnswer ? Colors.white : Colors.black,
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),

          // Option text
          Expanded(child: Text(optionText)),

          // Indicator icons
          if (isUserAnswer)
            Icon(
              isCorrectAnswer ? Icons.check_circle : Icons.cancel,
              color: isCorrectAnswer
                  ? getAppColor(context).correctAnswerColor
                  : getAppColor(context).incorrectAnswerColor,
            )
          else if (isCorrectAnswer)
            Icon(Icons.check_circle, color: getAppColor(context).correctAnswerColor),
        ],
      ),
    );
  }
}

class _StatusBadge extends StatelessWidget {
  const _StatusBadge({super.key, required this.context, required this.isAnswered, required this.isCorrect});

  final BuildContext context;
  final bool isAnswered;
  final bool isCorrect;

  @override
  Widget build(BuildContext context) {
    if (!isAnswered) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: getAppColor(context).notAnsweredBackgroundColor,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: getAppColor(context).notAnsweredColor),
        ),
        child: Text(
          'Not Answered',
          style: TextStyle(color: getAppColor(context).notAnsweredColor, fontWeight: FontWeight.bold)
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: isCorrect
            ? getAppColor(context).correctAnswerBackgroundColor.withAlpha(51)
            : getAppColor(context).incorrectAnswerBackgroundColor.withAlpha(51),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isCorrect
              ? getAppColor(context).correctAnswerColor
              : getAppColor(context).incorrectAnswerColor
        ),
      ),
      child: Text(
        isCorrect ? 'Correct' : 'Incorrect',
        style: TextStyle(
          color: isCorrect
              ? getAppColor(context).correctAnswerColor
              : getAppColor(context).incorrectAnswerColor,
          fontWeight: FontWeight.bold
        ),
      ),
    );
  }
}
