import 'package:cloud_functions/cloud_functions.dart';
import 'package:providers/common.dart';
import '../widgets/report_question_dialog.dart';

/// Service for reporting question issues
class QuestionReportService {
  final FirebaseFunctions _functions;

  QuestionReportService({FirebaseFunctions? functions})
      : _functions = functions ?? FirebaseFunctions.instance;

  /// Submit a report for a question
  Future<void> reportQuestion({
    required String testId,
    required int questionIndex,
    required QuestionIssueType issueType,
    String? comments,
  }) async {
    try {
      // Call the Cloud Function to report the question
      final result = await _functions.httpsCallable('reportQuestionIssue').call({
        'testId': testId,
        'questionIndex': questionIndex,
        'issueType': issueType.name,
        'comments': comments ?? '',
        'timestamp': DateTime.now().toIso8601String(),
      });
      
      dbgPrint('Report submitted successfully: ${result.data}');
    } catch (e) {
      dbgPrint('Error reporting question: $e');
      throw Exception('Failed to report question: $e');
    }
  }
}