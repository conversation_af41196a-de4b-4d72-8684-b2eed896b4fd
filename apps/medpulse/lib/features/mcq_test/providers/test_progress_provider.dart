import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:entities/question_entity.dart';
import 'package:entities/test_progress.dart';
import 'package:entities/test_result.dart';
import 'package:uuid/uuid.dart';
import '../../../common/string_util.dart';
import '../../../providers/question_provider.dart';
import '../../../providers/test_provider.dart';
import '../../../providers/user_provider.dart';

part 'test_progress_provider.g.dart';

/// Provider for managing test progress
@riverpod
class TestProgressNotifier extends _$TestProgressNotifier {
  @override
  Future<TestProgress> build(String testId, {bool free = true}) async {
    return TestProgress(
      currentIndex: 0,
      userAnswers: {},
      startTime: DateTime.now(),
      markedForReview: {},
      bookmarkedQuestions: {},
    );
  }

  // Select an answer for the current question
  // Returns true if the answer was selected, false if it was blocked (e.g., already revealed)
  bool selectAnswer(int optionIndex) {
    bool answerSelected = false;

    state.whenData((progress) {
      // Check if the answer for this question has been revealed
      if (!progress.revealedAnswers.contains(progress.currentIndex)) {
        final newAnswers = Map<int, int>.from(progress.userAnswers);
        newAnswers[progress.currentIndex] = optionIndex;

        state = AsyncValue.data(progress.copyWith(userAnswers: newAnswers));
        answerSelected = true;
      }
    });

    return answerSelected;
  }

  // Navigate to the next question
  void goToNextQuestion(int totalQuestions) {
    state.whenData((progress) {
      if (progress.currentIndex < totalQuestions - 1) {
        state = AsyncValue.data(progress.copyWith(currentIndex: progress.currentIndex + 1));
      }
    });
  }

  // Navigate to the previous question
  void goToPreviousQuestion() {
    state.whenData((progress) {
      if (progress.currentIndex > 0) {
        state = AsyncValue.data(progress.copyWith(currentIndex: progress.currentIndex - 1));
      }
    });
  }

  // Navigate to a specific question by index
  void goToQuestion(int index, int totalQuestions) {
    state.whenData((progress) {
      if (index >= 0 && index < totalQuestions) {
        state = AsyncValue.data(progress.copyWith(currentIndex: index));
      }
    });
  }

  // Mark the test as completed and calculate test result
  Future<void> completeTest(int questionCount) async {
    final String testId = this.testId;
    final bool free = this.free;

    state.whenData((progress) async {
      // Calculate answers correctness
      final (correctAnswers, answersCorrectness) = await _calculateAnswersCorrectness(
        progress,
        questionCount,
        testId,
        free,
      );

      // Calculate score percentage
      final scorePercentage = questionCount > 0 ? (correctAnswers / questionCount) * 100 : 0.0;

      final testNameEncrypted = (await ref.read(testProvider(testId, free: free).future)).name;
      // decrypt
      final testName = StringUtil.dc(testNameEncrypted, StringUtil.gk(testId));

      // Create the test result
      final result = TestResult(
        testId: testId,
        testName: testName ?? '-unkown-',
        free: free,
        id: const Uuid().v4(),
        timestamp: DateTime.now(),
        scorePercentage: scorePercentage,
        correctAnswers: correctAnswers,
        questionCount: questionCount,
        answeredCount: progress.userAnswers.length,
        timeTaken: DateTime.now().difference(progress.startTime),
      );

      // Update state with completed status, answers correctness, and result
      state = AsyncValue.data(
        progress.copyWith(isCompleted: true, answersCorrectness: answersCorrectness, result: result),
      );

      // Add the test result to the user's results list
      await ref.read(userProvider.notifier).addTestResult(state.value!);
    });
  }

  // Calculate answers correctness and return the number of correct answers
  Future<(int, List<AnswerFilterType>)> _calculateAnswersCorrectness(
    TestProgress progress,
    int questionCount,
    String testId,
    bool free,
  ) async {
    int correctAnswers = 0;
    List<AnswerFilterType> answersCorrectness = List.filled(questionCount, AnswerFilterType.skipped);

    // Check each answered question
    for (final entry in progress.userAnswers.entries) {
      final questionIndex = entry.key;
      final userAnswerIndex = entry.value;

      // Fetch the correct question
      try {
        final question = await ref.read(questionProvider(testId, questionIndex, free: free).future);

        // Handle different question types
        if (question.type == QuestionType.mcq) {
          // For MCQ questions, compare with correctOptionIndex
          if (question.correctOptionIndex == userAnswerIndex) {
            correctAnswers++;
            answersCorrectness[questionIndex] = AnswerFilterType.correct;
          } else {
            answersCorrectness[questionIndex] = AnswerFilterType.incorrect;
          }
        } else if (question.type == QuestionType.flipCard) {
          // For FlipCard questions, 1 means "I got it right"
          if (userAnswerIndex == 1) {
            correctAnswers++;
            answersCorrectness[questionIndex] = AnswerFilterType.correct;
          } else if (userAnswerIndex == 2) {
            // 2 means "I got it wrong"
            answersCorrectness[questionIndex] = AnswerFilterType.incorrect;
          }
        }
      } catch (e) {
        // Skip this question if there's an error
        continue;
      }
    }

    return (correctAnswers, answersCorrectness);
  }

  // Toggle marking a question for review
  void toggleMarkedForReview(int questionIndex) {
    state.whenData((progress) {
      final markedQuestions = Set<int>.from(progress.markedForReview);

      if (markedQuestions.contains(questionIndex)) {
        markedQuestions.remove(questionIndex);
      } else {
        markedQuestions.add(questionIndex);
      }

      state = AsyncValue.data(progress.copyWith(markedForReview: markedQuestions));
    });
  }

  // Toggle bookmarking a question
  void toggleBookmarked(int questionIndex) {
    state.whenData((progress) {
      final bookmarkedQuestions = Set<int>.from(progress.bookmarkedQuestions);

      if (bookmarkedQuestions.contains(questionIndex)) {
        bookmarkedQuestions.remove(questionIndex);
      } else {
        bookmarkedQuestions.add(questionIndex);
      }

      state = AsyncValue.data(progress.copyWith(bookmarkedQuestions: bookmarkedQuestions));
    });
  }

  // Get the next marked for review question
  int? getNextMarkedForReview(int currentIndex, int totalQuestions) {
    return state.valueOrNull?.markedForReview
        .where((index) => index > currentIndex && index < totalQuestions)
        .cast<int?>()
        .firstOrNull;
  }

  // Mark an answer as revealed
  void revealAnswer(int questionIndex) {
    state.whenData((progress) {
      final revealedAnswers = Set<int>.from(progress.revealedAnswers);
      revealedAnswers.add(questionIndex);
      state = AsyncValue.data(progress.copyWith(revealedAnswers: revealedAnswers));
    });
  }

  // Check if an answer has been revealed
  bool isAnswerRevealed(int questionIndex) {
    return state.valueOrNull?.revealedAnswers.contains(questionIndex) ?? false;
  }
}
