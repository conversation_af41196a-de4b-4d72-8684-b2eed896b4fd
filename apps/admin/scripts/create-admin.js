#!/usr/bin/env node

/**
 * MedPulse Admin User Creation Script
 *
 * Creates an admin user for the MedPulse admin panel.
 *
 * Usage:
 *   node create-admin.js --email <EMAIL> --password password123 --name "Admin User" --env dev|prod
 */

const admin = require('firebase-admin');
const fs = require('fs');

// Parse command line arguments
const args = process.argv.slice(2);
const options = {
  email: null,
  password: null,
  name: null,
  env: null,
  help: args.includes('--help')
};

// Parse arguments
for (let i = 0; i < args.length; i++) {
  switch (args[i]) {
    case '--email':
      options.email = args[i + 1];
      i++;
      break;
    case '--password':
      options.password = args[i + 1];
      i++;
      break;
    case '--name':
      options.name = args[i + 1];
      i++;
      break;
    case '--env':
      options.env = args[i + 1];
      i++;
      break;
  }
}

// Show help
if (options.help) {
  console.log(`
MedPulse Admin User Creation Script

Usage:
  node create-admin.js --email <EMAIL> --password password123 --name "Admin User" --env dev

Options:
  --email <email>           Admin email address (required)
  --password <password>     Admin password (required, min 6 characters)
  --name <display-name>     Admin display name (optional)
  --env <dev|prod>          Environment: dev or prod (required)
  --help                    Show this help message

Examples:
  npm run create-admin -- --email <EMAIL> --password MyPassword123 --name "Admin User" --env dev
  npm run create-admin -- --email <EMAIL> --password SecurePass123 --name "Admin" --env prod
`);
  process.exit(0);
}

// Validate environment and set project ID
if (!options.env) {
  console.error('❌ Error: Environment is required. Use --env dev or --env prod');
  process.exit(1);
}

if (!['dev', 'prod'].includes(options.env)) {
  console.error('❌ Error: Environment must be either "dev" or "prod"');
  process.exit(1);
}

// Set project ID based on environment
const projectId = options.env === 'dev' ? 'medpulsedev' : 'medpulse-prod';
const serviceAccountFile = options.env === 'dev' ? 'medpulsedev-service-account-key.json' : 'medpulse-prod-service-account-key.json';

if (!options.email) {
  console.error('❌ Error: Email is required. Use --email <email>');
  process.exit(1);
}

if (!options.password) {
  console.error('❌ Error: Password is required. Use --password <password>');
  process.exit(1);
}

if (options.password.length < 6) {
  console.error('❌ Error: Password must be at least 6 characters long.');
  process.exit(1);
}

// Validate email format
const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
if (!emailRegex.test(options.email)) {
  console.error('❌ Error: Invalid email format.');
  process.exit(1);
}

// Initialize Firebase Admin SDK
try {
  // Check for service account key file
  if (!fs.existsSync(serviceAccountFile)) {
    console.error(`❌ Service account key not found: ${serviceAccountFile}`);
    console.error('');
    console.error('🔧 Setup Instructions:');
    console.error(`   1. Go to Firebase Console > Project Settings > Service Accounts`);
    console.error(`   2. Click "Generate new private key"`);
    console.error(`   3. Download the JSON file`);
    console.error(`   4. Save it as "${serviceAccountFile}" in this directory`);
    console.error('');
    console.error(`🌐 Direct link: https://console.firebase.google.com/project/${projectId}/settings/serviceaccounts/adminsdk`);
    process.exit(1);
  }

  const serviceAccount = require(`./${serviceAccountFile}`);
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    projectId: projectId,
    databaseURL: `https://${projectId}-default-rtdb.firebaseio.com/`
  });
  console.log(`🔥 Connected to Firebase project: ${projectId} (${options.env})`);
} catch (error) {
  console.error('❌ Failed to initialize Firebase Admin SDK:', error.message);
  console.error('');
  console.error('🔧 Please ensure:');
  console.error('   1. Service account key file exists and is valid');
  console.error('   2. The key has proper permissions for this project');
  console.error('   3. The project ID is correct');
  process.exit(1);
}

// Main function to create admin user
async function createAdminUser() {
  try {
    const auth = admin.auth();

    // Try to get Firestore instance
    let db;
    try {
      db = admin.firestore();
    } catch (firestoreError) {
      console.log('⚠️  Firestore initialization issue, trying alternative approach...');
      // Import Firestore separately
      const { getFirestore } = require('firebase-admin/firestore');
      db = getFirestore();
    }

    console.log('\n👤 Creating Admin User');
    console.log('='.repeat(50));
    console.log(`📧 Email: ${options.email}`);
    console.log(`👤 Name: ${options.name || 'Not specified'}`);
    console.log(`🔐 Role: Admin`);
    console.log(`🌍 Environment: ${options.env}`);
    console.log(`📋 Project: ${projectId}`);

    // Check if user already exists
    let userExists = false;
    try {
      const existingUser = await auth.getUserByEmail(options.email);
      console.log(`⚠️  User with email ${options.email} already exists (UID: ${existingUser.uid})`);
      console.log('📝 Updating existing user to super admin...');
      userExists = true;
    } catch (error) {
      console.log('➕ Creating new user...');
    }

    // Create or update user
    let userRecord;
    try {
      if (userExists) {
        // Update existing user
        userRecord = await auth.getUserByEmail(options.email);
        userRecord = await auth.updateUser(userRecord.uid, {
          email: options.email,
          password: options.password,
          displayName: options.name || undefined,
          emailVerified: true
        });
      } else {
        // Create new user
        userRecord = await auth.createUser({
          email: options.email,
          password: options.password,
          displayName: options.name || undefined,
          emailVerified: true
        });
      }

      console.log(`✅ User ${userExists ? 'updated' : 'created'} successfully!`);
      console.log(`   • UID: ${userRecord.uid}`);
      console.log(`   • Email: ${userRecord.email}`);
      console.log(`   • Display Name: ${userRecord.displayName || 'Not set'}`);

      const now = new Date().toISOString();

      // Create user_claims document (triggers Firebase Function to set custom claims)
      console.log('🔐 Creating user_claims document...');
      await db.collection('user_claims').doc(userRecord.uid).set({
        role: 'admin'
      });
      console.log('✅ User claims document created');

      // Create user document in users collection (following UserEntity structure)
      console.log('📄 Creating user document...');
      const userDoc = {
        uid: userRecord.uid,
        displayName: userRecord.displayName || '',
        email: userRecord.email,
        role: 'admin',
        signedUp: now,
        isActive: true,
        roleAssignedAt: now,
        roleAssignedBy: 'system',
        provider: ['password'],
        lastLoginAt: null,
        // Optional fields that can be null initially
        courseId: null,
        yearId: null,
        bookmarks: [],
        results: [],
        subscriptions: {},
        transactions: {},
        acceptedTerms: null,
        acceptedPrivacyPolicy: null,
        acceptedTermsAt: null,
        acceptedPrivacyPolicyAt: null
      };

      await db.collection('users').doc(userRecord.uid).set(userDoc);
      console.log('✅ User document created');

      console.log('\n🎉 Admin user created successfully!');
      console.log('📊 Summary:');
      console.log(`   • Firebase Auth: ✅ User ${userExists ? 'updated' : 'created'}`);
      console.log(`   • User Claims: ✅ Document created (Firebase Function will sync to custom claims)`);
      console.log(`   • User Document: ✅ Created in users collection`);
      console.log(`   • Environment: ✅ ${options.env}`);
      console.log('\n🔐 Login Credentials:');
      console.log(`   • Email: ${options.email}`);
      console.log(`   • Password: [hidden for security]`);
      console.log(`   • Role: Admin`);
      console.log(`   • Environment: ${options.env}`);
      console.log('\n⏳ Important Note:');
      console.log('   • Firebase Function needs a few seconds to sync custom claims');
      console.log('   • If login fails immediately, wait 10-15 seconds and try again');
      console.log('   • The admin panel has automatic retry logic to handle this');

    } catch (error) {
      console.error('❌ Failed to create/update user:', error.message);
      process.exit(1);
    }

  } catch (error) {
    console.error('❌ Failed to create admin user:', error.message);
    process.exit(1);
  }
}

// Run the script
createAdminUser().then(() => {
  console.log('\n✅ Script completed successfully!');
  process.exit(0);
}).catch((error) => {
  console.error('❌ Script failed:', error.message);
  process.exit(1);
});
