{"name": "limiter", "main": "index.js", "version": "1.0.5", "homepage": "https://github.com/jhurliman/node-rate-limiter", "authors": ["<PERSON> <j<PERSON><PERSON><PERSON>@cull.tv>"], "description": "A generic rate limiter for node.js. Useful for API clients, web crawling, or other tasks that need to be throttled", "moduleType": ["node"], "keywords": ["rate", "limiting", "throttling"], "license": "MIT", "ignore": ["package.json"]}