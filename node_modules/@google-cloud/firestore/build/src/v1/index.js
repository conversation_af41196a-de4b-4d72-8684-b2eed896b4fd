"use strict";
// Copyright 2019 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
Object.defineProperty(exports, "__esModule", { value: true });
exports.FirestoreAdminClient = exports.FirestoreClient = void 0;
const firestore_admin_client_1 = require("./firestore_admin_client");
Object.defineProperty(exports, "FirestoreAdminClient", { enumerable: true, get: function () { return firestore_admin_client_1.FirestoreAdminClient; } });
const firestore_client_1 = require("./firestore_client");
Object.defineProperty(exports, "FirestoreClient", { enumerable: true, get: function () { return firestore_client_1.FirestoreClient; } });
// Doing something really horrible for reverse compatibility with original JavaScript exports
const existingExports = module.exports;
module.exports = firestore_client_1.FirestoreClient;
module.exports = Object.assign(module.exports, existingExports);
//# sourceMappingURL=index.js.map