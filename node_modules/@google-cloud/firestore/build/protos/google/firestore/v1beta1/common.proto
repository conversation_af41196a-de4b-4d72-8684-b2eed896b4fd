// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.firestore.v1beta1;

import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Firestore.V1Beta1";
option go_package = "cloud.google.com/go/firestore/apiv1beta1/firestorepb;firestorepb";
option java_multiple_files = true;
option java_outer_classname = "CommonProto";
option java_package = "com.google.firestore.v1beta1";
option objc_class_prefix = "GCFS";
option php_namespace = "Google\\Cloud\\Firestore\\V1beta1";
option ruby_package = "Google::Cloud::Firestore::V1beta1";

// A set of field paths on a document.
// Used to restrict a get or update operation on a document to a subset of its
// fields.
// This is different from standard field masks, as this is always scoped to a
// [Document][google.firestore.v1beta1.Document], and takes in account the dynamic nature of [Value][google.firestore.v1beta1.Value].
message DocumentMask {
  // The list of field paths in the mask. See [Document.fields][google.firestore.v1beta1.Document.fields] for a field
  // path syntax reference.
  repeated string field_paths = 1;
}

// A precondition on a document, used for conditional operations.
message Precondition {
  // The type of precondition.
  oneof condition_type {
    // When set to `true`, the target document must exist.
    // When set to `false`, the target document must not exist.
    bool exists = 1;

    // When set, the target document must exist and have been last updated at
    // that time.
    google.protobuf.Timestamp update_time = 2;
  }
}

// Options for creating a new transaction.
message TransactionOptions {
  // Options for a transaction that can be used to read and write documents.
  message ReadWrite {
    // An optional transaction to retry.
    bytes retry_transaction = 1;
  }

  // Options for a transaction that can only be used to read documents.
  message ReadOnly {
    // The consistency mode for this transaction. If not set, defaults to strong
    // consistency.
    oneof consistency_selector {
      // Reads documents at the given time.
      // This may not be older than 60 seconds.
      google.protobuf.Timestamp read_time = 2;
    }
  }

  // The mode of the transaction.
  oneof mode {
    // The transaction can only be used for read operations.
    ReadOnly read_only = 2;

    // The transaction can be used for both read and write operations.
    ReadWrite read_write = 3;
  }
}
