/*! firebase-admin v13.4.0 */
"use strict";
/*!
 * Copyright 2021 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.storage = exports.securityRules = exports.remoteConfig = exports.projectManagement = exports.messaging = exports.machineLearning = exports.installations = exports.instanceId = exports.firestore = exports.database = exports.auth = exports.appCheck = void 0;
__exportStar(require("./credential/index"), exports);
var app_check_namespace_1 = require("./app-check/app-check-namespace");
Object.defineProperty(exports, "appCheck", { enumerable: true, get: function () { return app_check_namespace_1.appCheck; } });
var auth_namespace_1 = require("./auth/auth-namespace");
Object.defineProperty(exports, "auth", { enumerable: true, get: function () { return auth_namespace_1.auth; } });
var database_namespace_1 = require("./database/database-namespace");
Object.defineProperty(exports, "database", { enumerable: true, get: function () { return database_namespace_1.database; } });
var firestore_namespace_1 = require("./firestore/firestore-namespace");
Object.defineProperty(exports, "firestore", { enumerable: true, get: function () { return firestore_namespace_1.firestore; } });
var instance_id_namespace_1 = require("./instance-id/instance-id-namespace");
Object.defineProperty(exports, "instanceId", { enumerable: true, get: function () { return instance_id_namespace_1.instanceId; } });
var installations_namespace_1 = require("./installations/installations-namespace");
Object.defineProperty(exports, "installations", { enumerable: true, get: function () { return installations_namespace_1.installations; } });
var machine_learning_namespace_1 = require("./machine-learning/machine-learning-namespace");
Object.defineProperty(exports, "machineLearning", { enumerable: true, get: function () { return machine_learning_namespace_1.machineLearning; } });
var messaging_namespace_1 = require("./messaging/messaging-namespace");
Object.defineProperty(exports, "messaging", { enumerable: true, get: function () { return messaging_namespace_1.messaging; } });
var project_management_namespace_1 = require("./project-management/project-management-namespace");
Object.defineProperty(exports, "projectManagement", { enumerable: true, get: function () { return project_management_namespace_1.projectManagement; } });
var remote_config_namespace_1 = require("./remote-config/remote-config-namespace");
Object.defineProperty(exports, "remoteConfig", { enumerable: true, get: function () { return remote_config_namespace_1.remoteConfig; } });
var security_rules_namespace_1 = require("./security-rules/security-rules-namespace");
Object.defineProperty(exports, "securityRules", { enumerable: true, get: function () { return security_rules_namespace_1.securityRules; } });
var storage_namespace_1 = require("./storage/storage-namespace");
Object.defineProperty(exports, "storage", { enumerable: true, get: function () { return storage_namespace_1.storage; } });
