/*! firebase-admin v13.4.0 */
/*!
 * Copyright 2021 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { App } from '../app';
import { Messaging as TMessaging } from './messaging';
import { AndroidConfig as TAndroidConfig, AndroidFcmOptions as TAndroidFcmOptions, AndroidNotification as TAndroidNotification, ApnsConfig as TApnsConfig, ApnsFcmOptions as TApnsFcmOptions, ApnsPayload as TApnsPayload, A<PERSON> as TAps, <PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON>A<PERSON><PERSON><PERSON><PERSON>, BatchResponse as TBatchResponse, CriticalSound as TCriticalSound, ConditionMessage as TConditionMessage, FcmOptions as TFcmOptions, LightSettings as TLightSettings, Message as TMessage, MessagingTopicManagementResponse as TMessagingTopicManagementResponse, MulticastMessage as TMulticastMessage, Notification as TNotification, SendResponse as TSendResponse, TokenMessage as TTokenMessage, TopicMessage as TTopicMessage, WebpushConfig as TWebpushConfig, WebpushFcmOptions as TWebpushFcmOptions, WebpushNotification as TWebpushNotification, DataMessagePayload as TDataMessagePayload, MessagingOptions as TMessagingOptions, MessagingPayload as TMessagingPayload, NotificationMessagePayload as TNotificationMessagePayload } from './messaging-api';
/**
 * Gets the {@link firebase-admin.messaging#Messaging} service for the
 * default app or a given app.
 *
 * `admin.messaging()` can be called with no arguments to access the default
 * app's `Messaging` service or as `admin.messaging(app)` to access the
 * `Messaging` service associated with a specific app.
 *
 * @example
 * ```javascript
 * // Get the Messaging service for the default app
 * var defaultMessaging = admin.messaging();
 * ```
 *
 * @example
 * ```javascript
 * // Get the Messaging service for a given app
 * var otherMessaging = admin.messaging(otherApp);
 * ```
 *
 * @param app - Optional app whose `Messaging` service to
 *   return. If not provided, the default `Messaging` service will be returned.
 *
 * @returns The default `Messaging` service if no
 *   app is provided or the `Messaging` service associated with the provided
 *   app.
 */
export declare function messaging(app?: App): messaging.Messaging;
export declare namespace messaging {
    /**
     * Type alias to {@link firebase-admin.messaging#Messaging}.
     */
    type Messaging = TMessaging;
    /**
     * Type alias to {@link firebase-admin.messaging#AndroidConfig}.
     */
    type AndroidConfig = TAndroidConfig;
    /**
     * Type alias to {@link firebase-admin.messaging#AndroidFcmOptions}.
     */
    type AndroidFcmOptions = TAndroidFcmOptions;
    /**
     * Type alias to {@link firebase-admin.messaging#AndroidNotification}.
     */
    type AndroidNotification = TAndroidNotification;
    /**
     * Type alias to {@link firebase-admin.messaging#ApnsConfig}.
     */
    type ApnsConfig = TApnsConfig;
    /**
     * Type alias to {@link firebase-admin.messaging#ApnsFcmOptions}.
     */
    type ApnsFcmOptions = TApnsFcmOptions;
    /**
     * Type alias to {@link firebase-admin.messaging#ApnsPayload}.
     */
    type ApnsPayload = TApnsPayload;
    /**
     * Type alias to {@link firebase-admin.messaging#Aps}.
     */
    type Aps = TAps;
    /**
     * Type alias to {@link firebase-admin.messaging#ApsAlert}.
     */
    type ApsAlert = TApsAlert;
    /**
     * Type alias to {@link firebase-admin.messaging#BatchResponse}.
     */
    type BatchResponse = TBatchResponse;
    /**
     * Type alias to {@link firebase-admin.messaging#CriticalSound}.
     */
    type CriticalSound = TCriticalSound;
    /**
     * Type alias to {@link firebase-admin.messaging#ConditionMessage}.
     */
    type ConditionMessage = TConditionMessage;
    /**
     * Type alias to {@link firebase-admin.messaging#FcmOptions}.
     */
    type FcmOptions = TFcmOptions;
    /**
     * Type alias to {@link firebase-admin.messaging#LightSettings}.
     */
    type LightSettings = TLightSettings;
    /**
     * Type alias to {@link firebase-admin.messaging#Message}.
     */
    type Message = TMessage;
    /**
     * Type alias to {@link firebase-admin.messaging#MessagingTopicManagementResponse}.
     */
    type MessagingTopicManagementResponse = TMessagingTopicManagementResponse;
    /**
     * Type alias to {@link firebase-admin.messaging#MulticastMessage}.
     */
    type MulticastMessage = TMulticastMessage;
    /**
     * Type alias to {@link firebase-admin.messaging#Notification}.
     */
    type Notification = TNotification;
    /**
     * Type alias to {@link firebase-admin.messaging#SendResponse}.
     */
    type SendResponse = TSendResponse;
    /**
     * Type alias to {@link firebase-admin.messaging#TokenMessage}.
     */
    type TokenMessage = TTokenMessage;
    /**
     * Type alias to {@link firebase-admin.messaging#TopicMessage}.
     */
    type TopicMessage = TTopicMessage;
    /**
     * Type alias to {@link firebase-admin.messaging#WebpushConfig}.
     */
    type WebpushConfig = TWebpushConfig;
    /**
     * Type alias to {@link firebase-admin.messaging#WebpushFcmOptions}.
     */
    type WebpushFcmOptions = TWebpushFcmOptions;
    /**
     * Type alias to {@link firebase-admin.messaging#WebpushNotification}.
     */
    type WebpushNotification = TWebpushNotification;
    /**
     * Type alias to {@link firebase-admin.messaging#DataMessagePayload}.
     */
    type DataMessagePayload = TDataMessagePayload;
    /**
     * Type alias to {@link firebase-admin.messaging#MessagingOptions}.
     */
    type MessagingOptions = TMessagingOptions;
    /**
     * Type alias to {@link firebase-admin.messaging#MessagingPayload}.
     */
    type MessagingPayload = TMessagingPayload;
    /**
     * Type alias to {@link firebase-admin.messaging#NotificationMessagePayload}.
     */
    type NotificationMessagePayload = TNotificationMessagePayload;
}
