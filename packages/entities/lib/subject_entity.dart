// packages/entities/lib/subject_entity.dart

import 'package:entities/entity.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import 'section_entity.dart';

part 'subject_entity.freezed.dart';
part 'subject_entity.g.dart';

/// SubjectEntity represents a subject within a year.
/// The subject name is the map key, not stored as a property.
/// Structure: subjects[subjectName] -> sections[sectionName] -> topics[topicName] -> tests
@Freezed(toJson: true)
class SubjectEntity extends Entity with _$SubjectEntity {
  factory SubjectEntity({
    @Default({}) Map<String, SectionEntity> sections,
  }) = _SubjectEntity;

  @override
  factory SubjectEntity.fromJson(Map<String, dynamic> json) => reportAnyJsonBugs(_$SubjectEntityFromJson, json);
}
