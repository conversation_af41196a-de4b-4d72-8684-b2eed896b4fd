// packages/entities/lib/courses_entity.dart

import 'package:entities/course_entity.dart';
import 'package:entities/entity.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'courses_entity.freezed.dart';
part 'courses_entity.g.dart';

/// CoursesEntity is the root entity stored in Firestore at 'course_catalog/structure'.
/// Contains the complete hierarchical course structure optimized for single-read operations.
/// Structure: courses[courseName] -> years[yearName] -> subjects[subjectName] -> sections[sectionName] -> topics[topicName] -> tests
/// All names are stored as map keys, not as entity properties.
@Freezed(toJson: true)
class CoursesEntity extends Entity with _$CoursesEntity {
  factory CoursesEntity({
    @Default({}) Map<String, CourseEntity> courses,
  }) = _CoursesEntity;

  @override
  factory CoursesEntity.fromJson(Map<String, dynamic> json) => reportAnyJsonBugs(_$CoursesEntityFromJson, json);
}
