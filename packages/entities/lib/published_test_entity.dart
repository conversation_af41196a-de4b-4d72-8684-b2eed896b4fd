import 'package:entities/entity.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import 'question_entity.dart';
import 'test_entity.dart';
import 'test_enums.dart';

part 'published_test_entity.freezed.dart';
part 'published_test_entity.g.dart';

@freezed
class PublishedTestEntity extends Entity with _$PublishedTestEntity {
  factory PublishedTestEntity({
    String? id,
    DateTime? createdAt,
    TestTier? tier,
    DateTime? updatedAt,
    required String courseId,
    required String yearId,
    required String subjectId,
    required String name,
    required QuestionType type,
    required int duration,
    required String subscriptionId,
    @Default(0) int questionCount,
    @Default(0) int fullQuestionCount,
    @Default(1) int version,
    @Default(TestDifficulty.easy) TestDifficulty difficulty,
    @Default(TestStatus.draft) TestStatus status,
    @Default(TestMode.free) TestMode mode,
    @Default([]) List<QuestionEntity> questions,
  }) = _PublishedTestEntity;

  @override
  factory PublishedTestEntity.fromJson(Map<String, dynamic> json) =>
      reportAnyJsonBugs(_$PublishedTestEntityFromJson, json);
}
