// packages/entities/lib/section_entity.dart

import 'package:entities/entity.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import 'topic_entity.dart';

part 'section_entity.freezed.dart';
part 'section_entity.g.dart';

/// SectionEntity represents a section within a subject.
/// The section name is the map key, not stored as a property.
/// Sections are admin-only management fields for organizing topics.
/// Structure: sections[sectionName] -> topics[topicName] -> tests
@Freezed(toJson: true)
class SectionEntity extends Entity with _$SectionEntity {
  factory SectionEntity({
    @Default({}) Map<String, TopicEntity> topics,
  }) = _SectionEntity;

  @override
  factory SectionEntity.fromJson(Map<String, dynamic> json) => reportAnyJsonBugs(_$SectionEntityFromJson, json);
}
