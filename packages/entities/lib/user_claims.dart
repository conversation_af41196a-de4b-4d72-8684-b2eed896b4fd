// packages/entities/lib/user_claims.dart

import 'package:freezed_annotation/freezed_annotation.dart';

part 'user_claims.freezed.dart';
part 'user_claims.g.dart';

/// User claims for admin access control
/// Simplified to just check for admin access
@freezed
class UserClaims with _$UserClaims {
  const factory UserClaims({
    /// Admin role claim - either 'admin' or null
    String? role,
  }) = _UserClaims;

  factory UserClaims.fromJson(Map<String, dynamic> json) =>
    _$UserClaimsFromJson(json);
}

/// Extension for admin access checking - simplified to only admin role
extension UserClaimsPermissions on UserClaims {
  // Simple admin check - only admin role supported
  bool get isAdmin => role == 'admin';

  // All permissions are the same for admin users
  bool get canAccessAdmin => isAdmin;
  bool get canManageContent => isAdmin;
  bool get canCreateContent => isAdmin;
  bool get canReviewContent => isAdmin;
  bool get canViewAnalytics => isAdmin;
  bool get canPublishContent => isAdmin;
  bool get canManageCourses => isAdmin;
  bool get canManageTests => isAdmin;

  /// Get display name for the role - only admin supported
  String get displayName {
    switch (role) {
      case 'admin': return 'Admin';
      default: return 'User';
    }
  }

  /// Get role description - simplified for admin only
  String get description {
    switch (role) {
      case 'admin': return 'Full admin access to manage content and system';
      default: return 'Learning platform access';
    }
  }
}

/// Predefined role templates for easy creation - simplified for admin only
extension UserClaimsTemplates on UserClaims {
  static const UserClaims admin = UserClaims(role: 'admin');
  static const UserClaims user = UserClaims(role: null);
}


