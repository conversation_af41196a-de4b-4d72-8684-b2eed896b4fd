// packages/entities/lib/topic_entity.dart

import 'package:entities/entity.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import 'published_test_entity.dart';

part 'topic_entity.freezed.dart';
part 'topic_entity.g.dart';

/// TopicEntity represents a topic within a section.
/// The topic name is the map key, not stored as a property.
/// Topics are admin-only management fields and contain the actual tests.
/// Structure: topics[topicName] -> tests[testId] (where testId is generated)
@Freezed(toJson: true)
class TopicEntity extends Entity with _$TopicEntity {
  factory TopicEntity({
    @Default({}) Map<String, PublishedTestEntity> tests,
  }) = _TopicEntity;

  @override
  factory TopicEntity.fromJson(Map<String, dynamic> json) => reportAnyJsonBugs(_$TopicEntityFromJson, json);
}


