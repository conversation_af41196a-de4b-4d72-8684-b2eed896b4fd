// packages/entities/lib/course_entity.dart

import 'package:entities/entity.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import 'year_entity.dart';

part 'course_entity.freezed.dart';
part 'course_entity.g.dart';

/// CourseEntity represents a course in the hierarchical structure.
/// The course name is the map key, not stored as a property.
/// Structure: courses[courseName] -> years[yearName] -> subjects[subjectName] -> sections[sectionName] -> topics[topicName] -> tests
@Freezed(toJson: true)
class CourseEntity extends Entity with _$CourseEntity {
  factory CourseEntity({
    @Default({}) Map<String, YearEntity> years,
  }) = _CourseEntity;

  @override
  factory CourseEntity.fromJson(Map<String, dynamic> json) => reportAnyJsonBugs(_$CourseEntityFromJson, json);
}
