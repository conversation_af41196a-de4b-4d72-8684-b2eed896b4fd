// packages/entities/lib/year_entity.dart

import 'package:entities/entity.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import 'subject_entity.dart';

part 'year_entity.freezed.dart';
part 'year_entity.g.dart';

/// YearEntity represents a year within a course.
/// The year name is the map key, not stored as a property.
/// Structure: years[yearName] -> subjects[subjectName] -> sections[sectionName] -> topics[topicName] -> tests
@Freezed(toJson: true)
class YearEntity extends Entity with _$YearEntity {
  factory YearEntity({
    @Default({}) Map<String, SubjectEntity> subjects,
  }) = _YearEntity;

  @override
  factory YearEntity.fromJson(Map<String, dynamic> json) => reportAnyJsonBugs(_$YearEntityFromJson, json);
}
