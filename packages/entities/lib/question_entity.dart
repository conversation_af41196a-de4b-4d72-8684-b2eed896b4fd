// packages/entities/lib/question_entity.dart

import 'package:entities/entity.dart';
import 'package:entities/timestamp_converter.dart';
import 'package:entities/test_enums.dart';

enum QuestionType { mcq, flipCard }

class QuestionEntity extends Entity {
  final String? id;
  final String? questionImage;
  final double? questionImageWidth;
  final double? questionImageHeight;
  final String? answer;
  final String? answerImage;
  final double? answerImageWidth;
  final double? answerImageHeight;
  final String? explanation;
  final String? explanationImage;
  final double? explanationImageWidth;
  final double? explanationImageHeight;
  final String? reference;
  final String? section;
  final String? topic;
  @TimestampConverter()
  final DateTime? createdAt;
  @TimestampConverter()
  final DateTime? updatedAt;
  final String courseId;
  final String yearId;
  final String subjectId;
  final String question;
  final QuestionType type;
  final TestDifficulty difficulty;
  final List<String> options;
  final int correctOptionIndex;
  final List<String> optionImages;
  final List<double> optionImagesWidth;
  final List<double> optionImagesHeight;
  final List<String> keywords;

  QuestionEntity({
    this.id,
    this.questionImage,
    this.questionImageWidth,
    this.questionImageHeight,
    this.answer,
    this.answerImage,
    this.answerImageWidth,
    this.answerImageHeight,
    this.explanation,
    this.explanationImage,
    this.explanationImageWidth,
    this.explanationImageHeight,
    this.reference,
    this.section,
    this.topic,
    this.createdAt,
    this.updatedAt,
    required this.courseId,
    required this.yearId,
    required this.subjectId,
    required this.question,
    required this.type,
    this.difficulty = TestDifficulty.easy,
    this.options = const [],
    this.correctOptionIndex = 0,
    this.optionImages = const [],
    this.optionImagesWidth = const [],
    this.optionImagesHeight = const [],
    this.keywords = const [],
  });

  static DateTime? _parseDateTime(dynamic value) {
    if (value == null) return null;
    if (value is DateTime) return value;
    if (value is String) return DateTime.parse(value);
    // Handle Firestore Timestamp
    if (value.runtimeType.toString() == 'Timestamp') {
      return (value as dynamic).toDate();
    }
    return null;
  }

  static List<String> _parseStringList(dynamic value) {
    if (value == null) return [];
    if (value is List) {
      try {
        return value.cast<String>();
      } catch (e) {
        return value.map((e) => e.toString()).toList();
      }
    }
    return [];
  }

  static List<double> _parseDoubleList(dynamic value) {
    if (value == null) return [];
    if (value is List) {
      try {
        return value.cast<double>();
      } catch (e) {
        return value.map((e) => (e as num).toDouble()).toList();
      }
    }
    return [];
  }

  factory QuestionEntity.fromJson(Map<String, dynamic> json) {
    return QuestionEntity(
      id: json['id'] as String?,
      questionImage: json['questionImage'] as String?,
      questionImageWidth: (json['questionImageWidth'] as num?)?.toDouble(),
      questionImageHeight: (json['questionImageHeight'] as num?)?.toDouble(),
      answer: json['answer'] as String?,
      answerImage: json['answerImage'] as String?,
      answerImageWidth: (json['answerImageWidth'] as num?)?.toDouble(),
      answerImageHeight: (json['answerImageHeight'] as num?)?.toDouble(),
      explanation: json['explanation'] as String?,
      explanationImage: json['explanationImage'] as String?,
      explanationImageWidth: (json['explanationImageWidth'] as num?)?.toDouble(),
      explanationImageHeight: (json['explanationImageHeight'] as num?)?.toDouble(),
      reference: json['reference'] as String?,
      section: json['section'] as String?,
      topic: json['topic'] as String?,
      createdAt: json['createdAt'] != null ? _parseDateTime(json['createdAt']) : null,
      updatedAt: json['updatedAt'] != null ? _parseDateTime(json['updatedAt']) : null,
      courseId: json['courseId'] as String,
      yearId: json['yearId'] as String,
      subjectId: json['subjectId'] as String,
      question: json['question'] as String,
      type: QuestionType.values.firstWhere((e) => e.name == json['type'], orElse: () => QuestionType.mcq),
      difficulty: TestDifficulty.values.firstWhere((e) => e.name == json['difficulty'], orElse: () => TestDifficulty.easy),
      options: _parseStringList(json['options']),
      correctOptionIndex: json['correctOptionIndex'] as int? ?? 0,
      optionImages: _parseStringList(json['optionImages']),
      optionImagesWidth: _parseDoubleList(json['optionImagesWidth']),
      optionImagesHeight: _parseDoubleList(json['optionImagesHeight']),
      keywords: _parseStringList(json['keywords']),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'questionImage': questionImage,
      'questionImageWidth': questionImageWidth,
      'questionImageHeight': questionImageHeight,
      'answer': answer,
      'answerImage': answerImage,
      'answerImageWidth': answerImageWidth,
      'answerImageHeight': answerImageHeight,
      'explanation': explanation,
      'explanationImage': explanationImage,
      'explanationImageWidth': explanationImageWidth,
      'explanationImageHeight': explanationImageHeight,
      'reference': reference,
      'section': section,
      'topic': topic,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'courseId': courseId,
      'yearId': yearId,
      'subjectId': subjectId,
      'question': question,
      'type': type.name,
      'difficulty': difficulty.name,
      'options': options,
      'correctOptionIndex': correctOptionIndex,
      'optionImages': optionImages,
      'optionImagesWidth': optionImagesWidth,
      'optionImagesHeight': optionImagesHeight,
      'keywords': keywords,
    };
  }

  QuestionEntity copyWith({
    String? id,
    String? questionImage,
    double? questionImageWidth,
    double? questionImageHeight,
    String? answer,
    String? answerImage,
    double? answerImageWidth,
    double? answerImageHeight,
    String? explanation,
    String? explanationImage,
    double? explanationImageWidth,
    double? explanationImageHeight,
    String? reference,
    String? section,
    String? topic,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? courseId,
    String? yearId,
    String? subjectId,
    String? question,
    QuestionType? type,
    TestDifficulty? difficulty,
    List<String>? options,
    int? correctOptionIndex,
    List<String>? optionImages,
    List<double>? optionImagesWidth,
    List<double>? optionImagesHeight,
    List<String>? keywords,
  }) {
    return QuestionEntity(
      id: id ?? this.id,
      questionImage: questionImage ?? this.questionImage,
      questionImageWidth: questionImageWidth ?? this.questionImageWidth,
      questionImageHeight: questionImageHeight ?? this.questionImageHeight,
      answer: answer ?? this.answer,
      answerImage: answerImage ?? this.answerImage,
      answerImageWidth: answerImageWidth ?? this.answerImageWidth,
      answerImageHeight: answerImageHeight ?? this.answerImageHeight,
      explanation: explanation ?? this.explanation,
      explanationImage: explanationImage ?? this.explanationImage,
      explanationImageWidth: explanationImageWidth ?? this.explanationImageWidth,
      explanationImageHeight: explanationImageHeight ?? this.explanationImageHeight,
      reference: reference ?? this.reference,
      section: section ?? this.section,
      topic: topic ?? this.topic,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      courseId: courseId ?? this.courseId,
      yearId: yearId ?? this.yearId,
      subjectId: subjectId ?? this.subjectId,
      question: question ?? this.question,
      type: type ?? this.type,
      difficulty: difficulty ?? this.difficulty,
      options: options ?? this.options,
      correctOptionIndex: correctOptionIndex ?? this.correctOptionIndex,
      optionImages: optionImages ?? this.optionImages,
      optionImagesWidth: optionImagesWidth ?? this.optionImagesWidth,
      optionImagesHeight: optionImagesHeight ?? this.optionImagesHeight,
      keywords: keywords ?? this.keywords,
    );
  }
}
