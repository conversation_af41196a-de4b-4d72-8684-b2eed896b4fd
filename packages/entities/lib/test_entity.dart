// packages/entities/lib/test_entity.dart

import 'package:entities/entity.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import 'question_entity.dart';
import 'test_enums.dart';

part 'test_entity.freezed.dart';
part 'test_entity.g.dart';

@JsonEnum(fieldRename: FieldRename.snake)
enum TestMode { free, paid }

@JsonEnum(fieldRename: FieldRename.snake)
enum TestTier { tier1, tier2, tier3 }

@freezed
class TestEntity extends Entity with _$TestEntity {
  factory TestEntity({
    String? id,
    DateTime? createdAt,
    TestTier? tier,
    DateTime? updatedAt,
    required String courseId,
    required String yearId,
    required String subjectId,
    required String name,
    required QuestionType type,
    required int duration,
    @Default(TestDifficulty.easy) TestDifficulty difficulty,
    @Default(TestStatus.draft) TestStatus status,
    @Default(TestMode.free) TestMode mode,
    @Default([]) List<String> questionIds,
  }) = _TestEntity;

  @override
  factory TestEntity.fromJson(Map<String, dynamic> json) =>
      reportAnyJsonBugs(_$TestEntityFromJson, json);
}
