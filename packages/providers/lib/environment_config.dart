/// Environment Configuration for MedPulse Admin
/// 
/// This class manages different environments (dev, staging, prod) using
/// <PERSON>lut<PERSON>'s built-in --dart-define system.
/// 
/// Usage:
/// - Development: flutter run --dart-define=ENVIRONMENT=dev
/// - Staging: flutter run --dart-define=ENVIRONMENT=staging  
/// - Production: flutter run (default)

class EnvironmentConfig {
  // Get environment from dart-define, default to production
  static const String _environment = String.fromEnvironment('ENVIRONMENT', defaultValue: 'prod');
  
  /// Current environment name
  static String get environment => _environment;
  
  /// Check if running in development
  static bool get isDevelopment => _environment == 'dev';
  
  /// Check if running in staging
  static bool get isStaging => _environment == 'staging';
  
  /// Check if running in production
  static bool get isProduction => _environment == 'prod';
  
  /// Firebase project ID based on environment
  static String get firebaseProjectId {
    switch (_environment) {
      case 'dev':
        return 'medpulsedev';
      case 'staging':
        return 'medpulsedev'; // Use same project for staging
      case 'prod':
      default:
        return 'medpulse-prod';
    }
  }
  
  /// App name with environment suffix
  static String get appName {
    switch (_environment) {
      case 'dev':
        return 'MedPulse Admin (Dev)';
      case 'staging':
        return 'MedPulse Admin (Staging)';
      case 'prod':
      default:
        return 'MedPulse Admin';
    }
  }
  
  /// Environment display name
  static String get environmentName {
    switch (_environment) {
      case 'dev':
        return 'Development';
      case 'staging':
        return 'Staging';
      case 'prod':
      default:
        return 'Production';
    }
  }
  
  /// Debug mode (enabled for dev and staging)
  static bool get isDebugMode => isDevelopment || isStaging;
  
  /// API base URL (if you have different APIs per environment)
  static String get apiBaseUrl {
    switch (_environment) {
      case 'dev':
        return 'https://dev-api.medpulse.com';
      case 'staging':
        return 'https://staging-api.medpulse.com';
      case 'prod':
      default:
        return 'https://api.medpulse.com';
    }
  }
  
  /// Print current environment info
  static void printEnvironmentInfo() {
    print('🚀 MedPulse Admin starting...');
    print('📱 App: $appName');
    print('🌍 Environment: $environmentName');
    print('🔥 Firebase Project: $firebaseProjectId');
    print('🐛 Debug Mode: $isDebugMode');
    if (isDebugMode) {
      print('⚠️  Running in ${environmentName.toLowerCase()} mode');
    }
  }
}
