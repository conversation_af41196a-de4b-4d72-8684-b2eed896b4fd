// packages/providers/lib/courses_provider.dart

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:entities/courses_entity.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'courses_provider.g.dart';

@riverpod
Future<CoursesEntity> courses(Ref ref) async {
  final snapshot = await FirebaseFirestore.instance.collection("course_catalog").doc("structure").get();

  if (!snapshot.exists) {
    return CoursesEntity();
  }

  return CoursesEntity.fromJson(snapshot.data() ?? {});
}
