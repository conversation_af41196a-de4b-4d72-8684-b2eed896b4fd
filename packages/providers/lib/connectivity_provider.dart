// packages/providers/lib/connectivity_provider.dart

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'connectivity_provider.g.dart';

@Riverpod(keepAlive: true)
Stream<bool> connectivity(Ref ref) async* {
  final connectivityStream = Connectivity().onConnectivityChanged;

  // push out the initial state
  var connected = await isConnected();
  debugPrint("connectivityProvider: Initial connectivity state is ${connected ? 'connected' : 'disconnected'}");
  yield connected;

  // and then all updates
  await for (final List<ConnectivityResult> connectivityResults in connectivityStream) {
    // FIXED: Properly handle List<ConnectivityResult> from connectivity_plus 6.x
    var connected = _isConnected(connectivityResults);
    debugPrint("connectivityProvider: Connectivity state change to ${connected ? 'connected' : 'disconnected'}");
    yield connected;
  }
}

Future<bool> isConnected() async {
  var connections = await Connectivity().checkConnectivity();
  return _isConnected(connections);
}

bool _isConnected(List<ConnectivityResult> connections) {
  for (var c in connections) {
    if (c != ConnectivityResult.none) return true;
  }
  return false;
}
