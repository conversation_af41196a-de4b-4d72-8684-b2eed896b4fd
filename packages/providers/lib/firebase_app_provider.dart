// packages/providers/lib/firebase_app_provider.dart

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'common.dart';

part 'firebase_app_provider.g.dart';

/// Initialize providers library before using it.
class ProvidersFirebaseOptions {
  static FirebaseOptions? firebaseOptions;

  ProvidersFirebaseOptions(FirebaseOptions options) {
    firebaseOptions = options;
  }
}

@Riverpod(keepAlive: true)
Future<FirebaseApp> firebaseApp(Ref ref) async {
  final f = "firebaseAppProvider";
  dbgPrint('$f: init');
  // FirebaseOptions must be set
  assert(ProvidersFirebaseOptions.firebaseOptions != null);
  // initialize Firebase framework
  try {
    var app = await Firebase.initializeApp(options: ProvidersFirebaseOptions.firebaseOptions);
    FirebaseFirestore.instance.settings = const Settings(persistenceEnabled: true);
    final useEmulator = const bool.fromEnvironment('USE_FIREBASE_EMULATOR', defaultValue: false);
    dbgPrint('$f: Emulator: $useEmulator');
    if (useEmulator) {
      try {
        // await FirebaseAuth.instance.useAuthEmulator('localhost', 9099);
        // FirebaseFirestore.instance.useFirestoreEmulator('localhost', 8080);
        FirebaseFunctions.instanceFor(region: 'asia-southeast1').useFunctionsEmulator('localhost', 5001);
        // FirebaseStorage.instance.useStorageEmulator('localhost', 9199);
        dbgPrint('$f: Firebase Emulators Initialized');
      } catch (e) {
        dbgPrint('$f: Failed to initialize Firebase Emulators: $e');
      }
    }

    // get user and password from environment variables
    final user = const String.fromEnvironment('email', defaultValue: '');
    final password = const String.fromEnvironment('password', defaultValue: '');
    dbgPrint('$f: User: $user');
    if (user.isNotEmpty && password.isNotEmpty) {
      // sign in with email and password
      dbgPrint('$f: Signing in with email and password');
      await FirebaseAuth.instance.signInWithEmailAndPassword(email: user, password: password);
    }

/*
    FirebaseFunctions.instanceFor(
      region: 'asia-southeast1', // Replace with your desired region
    );
*/
    return app;
  } on FirebaseException catch (e) {
    if (e.code != 'duplicate-app') rethrow;
  }
  return Firebase.app();
}
