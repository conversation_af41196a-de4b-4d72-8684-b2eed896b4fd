name: providers
description: "Providers for MedPulse"
version: 0.0.1
publish_to: none

environment:
  sdk: ^3.6.1
  flutter: ">=1.17.0"

dependencies:
  entities:
    path: ../entities
  storage_service:
    path: ../storage_service
  cloud_firestore: ^5.6.2
  connectivity_plus: ^6.1.2
  crypto: ^3.0.6
  firebase_analytics: ^11.4.1
  firebase_auth: ^5.4.1
  firebase_core: ^3.10.1
  flutter:
    sdk: flutter
  flutter_riverpod: ^2.6.1
  freezed_annotation: ^2.4.4
  google_sign_in: ^6.2.2
  json_annotation: ^4.9.0
  riverpod_annotation: ^2.6.1
  sign_in_with_apple: ^6.1.4
  cloud_functions: ^5.3.3
  firebase_storage: ^12.4.3

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  build_runner: ^2.4.14
  freezed: ^2.5.8
  json_serializable: ^6.9.3
  riverpod_generator: ^2.6.4
  custom_lint: ^0.7.1
  riverpod_lint: ^2.6.4

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

# To add assets to your package, add an assets section, like this:
# assets:
#   - images/a_dot_burr.jpeg
#   - images/a_dot_ham.jpeg
#
# For details regarding assets in packages, see
# https://flutter.dev/to/asset-from-package
#
# An image asset can refer to one or more resolution-specific "variants", see
# https://flutter.dev/to/resolution-aware-images

# To add custom fonts to your package, add a fonts section here,
# in this "flutter" section. Each entry in this list should have a
# "family" key with the font family name, and a "fonts" key with a
# list giving the asset and other descriptors for the font. For
# example:
# fonts:
#   - family: Schyler
#     fonts:
#       - asset: fonts/Schyler-Regular.ttf
#       - asset: fonts/Schyler-Italic.ttf
#         style: italic
#   - family: Trajan Pro
#     fonts:
#       - asset: fonts/TrajanPro.ttf
#       - asset: fonts/TrajanPro_Bold.ttf
#         weight: 700
#
# For details regarding fonts in packages, see
# https://flutter.dev/to/font-from-package
