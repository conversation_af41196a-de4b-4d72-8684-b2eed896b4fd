import {onCall, HttpsError} from 'firebase-functions/v2/https';
import {logger} from 'firebase-functions';
import {getAuth} from 'firebase-admin/auth';
import {getFirestore} from 'firebase-admin/firestore';

interface DeleteUserRequest {
  userId: string;
}

interface DeleteUserResponse {
  success: boolean;
  message: string;
  deletedUserId: string;
}

export const deleteUser = onCall({
  region: 'asia-southeast1',
  memory: '256MiB',
  maxInstances: 10,
  timeoutSeconds: 60,
}, async (request) => {
  const auth = getAuth();
  const firestore = getFirestore();

  // Verify the caller is authenticated and has admin privileges
  if (!request.auth) {
    throw new HttpsError('unauthenticated', 'User must be authenticated');
  }

  // Check if the caller has admin privileges using new role-based system
  const callerUid = request.auth.uid;
  const callerClaims = request.auth.token;
  const callerRole = callerClaims.role as string;

  // Only super_admin can delete users (user management permission)
  if (callerRole !== 'super_admin') {
    throw new HttpsError(
      'permission-denied',
      'You do not have permission to delete users. Please contact your administrator.'
    );
  }

  // Validate request data
  const data = request.data as DeleteUserRequest;
  if (!data.userId) {
    throw new HttpsError('invalid-argument', 'userId is required');
  }

  // Prevent self-deletion
  if (data.userId === callerUid) {
    throw new HttpsError(
      'invalid-argument',
      'Cannot delete your own account'
    );
  }

  try {
    logger.info(`Admin ${callerUid} deleting user: ${data.userId}`);

    // First, verify the user exists in Firebase Auth
    let userRecord;
    try {
      userRecord = await auth.getUser(data.userId);
    } catch (error: any) {
      if (error.code === 'auth/user-not-found') {
        // User doesn't exist in Firebase Auth, but might exist in Firestore
        logger.warn(`User ${data.userId} not found in Firebase Auth, proceeding with Firestore cleanup`);
      } else {
        throw error;
      }
    }

    // Delete from Firestore in a transaction
    await firestore.runTransaction(async (transaction) => {
      // Delete user document
      const userRef = firestore.collection('users').doc(data.userId);
      transaction.delete(userRef);

      // Delete user claims
      const claimsRef = firestore.collection('user_claims').doc(data.userId);
      transaction.delete(claimsRef);

      // Delete user attempts subcollection
      const attemptsQuery = await firestore
        .collection('users')
        .doc(data.userId)
        .collection('attempts')
        .get();

      for (const doc of attemptsQuery.docs) {
        transaction.delete(doc.ref);
      }
    });

    // Delete from Firebase Auth (if user exists)
    if (userRecord) {
      await auth.deleteUser(data.userId);
      logger.info(`Successfully deleted user ${data.userId} from Firebase Auth`);
    }

    logger.info(`Successfully deleted user ${data.userId} completely`);

    const response: DeleteUserResponse = {
      success: true,
      message: `User ${data.userId} has been completely deleted`,
      deletedUserId: data.userId,
    };

    return response;
  } catch (error: any) {
    logger.error(`Error deleting user ${data.userId}:`, error);

    // Handle specific error cases
    if (error.code === 'auth/user-not-found') {
      throw new HttpsError(
        'not-found',
        `User ${data.userId} not found in Firebase Authentication`
      );
    } else if (error.code === 'permission-denied') {
      throw new HttpsError(
        'permission-denied',
        'Insufficient permissions to delete user'
      );
    } else {
      throw new HttpsError(
        'internal',
        `Failed to delete user: ${error.message}`
      );
    }
  }
});
