import {onCall, HttpsError} from 'firebase-functions/v2/https';
import {logger} from 'firebase-functions';
import {getAuth} from 'firebase-admin/auth';
import {getFirestore} from 'firebase-admin/firestore';

interface CreateUserRequest {
  displayName: string;
  email: string;
  password: string;
  role: string;
  isActive: boolean;
}

interface CreateUserResponse {
  uid: string;
  displayName: string;
  email: string;
  role: string;
  isActive: boolean;
  signedUp: string;
  roleAssignedAt: string;
  roleAssignedBy: string;
}

export const createUser = onCall({
  region: 'asia-southeast1',
  memory: '256MiB',
  maxInstances: 10,
  timeoutSeconds: 60,
}, async (request) => {
  const auth = getAuth();
  const firestore = getFirestore();

  // Verify the caller is authenticated and has admin privileges
  if (!request.auth) {
    throw new HttpsError('unauthenticated', 'User must be authenticated');
  }

  const callerUid = request.auth.uid;

  // Check if caller has admin privileges using new role-based system
  try {
    const callerRecord = await auth.getUser(callerUid);
    const customClaims = callerRecord.customClaims || {};
    const callerRole = customClaims.role as string;

    // Only super_admin can create users (user management permission)
    if (callerRole !== 'super_admin') {
      throw new HttpsError('permission-denied', 'You do not have permission to create users. Please contact your administrator.');
    }
  } catch (error) {
    if (error instanceof HttpsError) {
      throw error; // Re-throw HttpsError as-is
    }
    logger.error('Error verifying admin privileges:', error);
    throw new HttpsError('permission-denied', 'Unable to verify admin privileges');
  }

  // Validate request data
  const data = request.data as CreateUserRequest;

  if (!data.displayName || !data.email || !data.password || !data.role) {
    throw new HttpsError('invalid-argument', 'Missing required fields: displayName, email, password, role');
  }

  if (data.password.length < 6) {
    throw new HttpsError('invalid-argument', 'Password must be at least 6 characters long');
  }

  // Validate email format
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(data.email)) {
    throw new HttpsError('invalid-argument', 'Invalid email format');
  }

  // Validate role (using snake_case as expected by the admin app)
  const validRoles = ['super_admin', 'manager', 'content_creator', 'student'];
  if (!validRoles.includes(data.role)) {
    throw new HttpsError('invalid-argument', `Invalid role. Must be one of: ${validRoles.join(', ')}`);
  }

  try {
    logger.info(`Admin ${callerUid} creating user with email: ${data.email}`);

    // Create user with Firebase Auth Admin SDK
    const userRecord = await auth.createUser({
      email: data.email,
      password: data.password,
      displayName: data.displayName,
      emailVerified: false, // User should verify their email
    });

    const newUserUid = userRecord.uid;
    const now = new Date().toISOString();

    // Create user document in Firestore
    const userEntity = {
      uid: newUserUid,
      displayName: data.displayName,
      email: data.email,
      role: data.role,
      isActive: data.isActive,
      provider: ['password'],
      signedUp: now,
      roleAssignedAt: now,
      roleAssignedBy: callerUid,
    };

    await firestore.collection('users').doc(newUserUid).set(userEntity);

    // Set user claims based on role
    const claims = getRoleBasedClaims(data.role);
    await auth.setCustomUserClaims(newUserUid, claims);

    // Also create user_claims document for consistency
    await firestore.collection('user_claims').doc(newUserUid).set(claims);

    logger.info(`Successfully created user ${newUserUid} with role ${data.role}`);

    // Return user data (excluding sensitive information)
    const response: CreateUserResponse = {
      uid: newUserUid,
      displayName: data.displayName,
      email: data.email,
      role: data.role,
      isActive: data.isActive,
      signedUp: now,
      roleAssignedAt: now,
      roleAssignedBy: callerUid,
    };

    return response;

  } catch (error: unknown) {
    logger.error('Error creating user:', error);

    // Handle specific Firebase Auth errors
    if (error && typeof error === 'object' && 'code' in error) {
      const firebaseError = error as {code: string; message: string};
      if (firebaseError.code === 'auth/email-already-exists') {
        throw new HttpsError('already-exists', 'A user with this email already exists');
      } else if (firebaseError.code === 'auth/invalid-email') {
        throw new HttpsError('invalid-argument', 'Invalid email address');
      } else if (firebaseError.code === 'auth/weak-password') {
        throw new HttpsError('invalid-argument', 'Password is too weak');
      }
    }

    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    throw new HttpsError('internal', `Failed to create user: ${errorMessage}`);
  }
});

/**
 * Get role-based claims for a user role (new single claim system)
 */
function getRoleBasedClaims(role: string): Record<string, any> {
  return {
    role: role,
  };
}
